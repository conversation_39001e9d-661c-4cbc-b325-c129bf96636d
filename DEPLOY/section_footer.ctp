</div>
</div>
<?php echo $this->element('chrome/usp_footer'); ?>

<div class="testimonial-extract testimonial-extract--strip testimonial-extract--home" itemscope itemtype="http://schema.org/Review">
    <div class="testimonial-extract__inner">
        <iframe class="feefo-widget js-feefo-widget" src="/feefo-iframe.html" frameborder="0" height="200" width="100%" scrolling="no"></iframe>
    </div>

    <!-- Schema.org Review structured data from Feefo API -->
    <div style="display: none;">
        <?php if (!empty($feefoReviews['feedback']) && is_array($feefoReviews['feedback'])): ?>
            <?php $review = $feefoReviews['feedback'][0]['FeefoReview']; // Use first review ?>
            <div itemprop="author" itemscope itemtype="http://schema.org/Person">
                <span itemprop="name"><?php echo htmlspecialchars($review['customer']); ?></span>
            </div>
            <div itemprop="itemReviewed" itemscope itemtype="http://schema.org/LocalBusiness">
                <span itemprop="name">Bon Voyage Travel & Tours Ltd</span>
                <link itemprop="url" href="https://www.bon-voyage.co.uk/">
                <div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
                    <span itemprop="streetAddress">16-18 Bellevue Road</span>
                    <span itemprop="addressLocality">Southampton</span>
                    <span itemprop="addressRegion">Hampshire</span>
                    <span itemprop="addressCountry">GB</span>
                    <span itemprop="postalCode">SO15 2AY</span>
                </div>
            </div>
            <span itemprop="reviewBody"><?php echo htmlspecialchars($review['comment'] ? $review['comment'] : $review['title']); ?></span>
            <div itemprop="reviewRating" itemscope itemtype="http://schema.org/Rating">
                <span itemprop="ratingValue"><?php echo (int)$review['rating']; ?></span>
                <span itemprop="bestRating">5</span>
            </div>
            <span itemprop="datePublished"><?php echo $review['date']; ?></span>
        <?php else: ?>
            <!-- Fallback if no reviews available -->
            <div itemprop="author" itemscope itemtype="http://schema.org/Organization">
                <span itemprop="name">Feefo</span>
                <link itemprop="url" href="https://www.feefo.com/">
            </div>
            <div itemprop="itemReviewed" itemscope itemtype="http://schema.org/LocalBusiness">
                <span itemprop="name">Bon Voyage Travel & Tours Ltd</span>
                <link itemprop="url" href="https://www.bon-voyage.co.uk/">
                <div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
                    <span itemprop="streetAddress">16-18 Bellevue Road</span>
                    <span itemprop="addressLocality">Southampton</span>
                    <span itemprop="addressRegion">Hampshire</span>
                    <span itemprop="addressCountry">GB</span>
                    <span itemprop="postalCode">SO15 2AY</span>
                </div>
            </div>
            <span itemprop="reviewBody">Excellent customer reviews and testimonials for Bon Voyage Travel & Tours Ltd based on <?php echo isset($feefoReviews['summary']['count']) ? $feefoReviews['summary']['count'] : '1000+'; ?> verified reviews</span>
            <div itemprop="reviewRating" itemscope itemtype="http://schema.org/Rating">
                <span itemprop="ratingValue"><?php echo isset($feefoReviews['summary']['average']) ? round($feefoReviews['summary']['average']) : 5; ?></span>
                <span itemprop="bestRating">5</span>
            </div>
        <?php endif; ?>
    </div>
</div>
</section>
