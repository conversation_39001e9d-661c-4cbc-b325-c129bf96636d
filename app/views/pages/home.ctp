<?php

if (isset($googleTracking)) {
    echo $googleTracking;
}

?>

<?php echo $this->element('chrome/usp_footer'); ?>

<?php echo $this->element('home/feature_panels'); ?>

<div class="testimonial-extract testimonial-extract--strip testimonial-extract--home" itemscope itemtype="http://schema.org/Review">
    <div class="testimonial-extract__inner">
        <iframe class="feefo-widget js-feefo-widget" src="/feefo-iframe.html" frameborder="0" height="200" width="100%" scrolling="no"></iframe>
    </div>

    <!-- Schema.org Review structured data - references LocalBusiness from feefo_microformat -->
    <div style="display: none;">
        <div itemprop="author" itemscope itemtype="http://schema.org/Organization">
            <span itemprop="name">Feefo</span>
            <link itemprop="url" href="https://www.feefo.com/">
        </div>
        <div itemprop="itemReviewed" itemref="#bonvoyage-business"></div>
        <span itemprop="reviewBody">Excellent customer reviews and testimonials for Bon Voyage Travel & Tours Ltd based on <?php echo isset($feefoReviews['summary']['count']) ? $feefoReviews['summary']['count'] : '1000+'; ?> verified reviews</span>
        <div itemprop="reviewRating" itemscope itemtype="http://schema.org/Rating">
            <span itemprop="ratingValue"><?php echo isset($feefoReviews['summary']['average']) ? $feefoReviews['summary']['average'] : 5; ?></span>
            <span itemprop="bestRating"><?php echo isset($feefoReviews['summary']['best']) ? $feefoReviews['summary']['best'] : 5; ?></span>
        </div>
    </div>
</div>
